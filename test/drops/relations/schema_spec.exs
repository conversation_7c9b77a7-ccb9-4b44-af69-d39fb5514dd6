defmodule Drops.Relations.SchemaSpec do
  use Drops.OperationCase, async: true

  describe "basic schema inference" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "infers basic fields from users table" do
      defmodule Test.Relations.Users do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      assert Test.Relations.Users.Struct.__schema__(:fields) == [:id, :name, :email]
      assert Test.Relations.Users.Struct.__schema__(:type, :id) == :id
      assert Test.Relations.Users.Struct.__schema__(:type, :name) == :string
      assert Test.Relations.Users.Struct.__schema__(:type, :email) == :string
    end
  end

  describe "different field types" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.BasicTypesSchema]
    test "infers various field types correctly" do
      defmodule Test.Relations.BasicTypes do
        use Drops.Relation, repo: Drops.TestRepo, name: "basic_types", infer: true
      end

      fields = Test.Relations.BasicTypes.Struct.__schema__(:fields)

      # Should include all non-timestamp fields
      assert :id in fields
      assert :string_field in fields
      assert :integer_field in fields
      assert :float_field in fields
      assert :boolean_field in fields
      assert :binary_field in fields
      assert :bitstring_field in fields

      # Check types
      assert Test.Relations.BasicTypes.Struct.__schema__(:type, :string_field) == :string

      assert Test.Relations.BasicTypes.Struct.__schema__(:type, :integer_field) ==
               :integer

      assert Test.Relations.BasicTypes.Struct.__schema__(:type, :float_field) == :float

      assert Test.Relations.BasicTypes.Struct.__schema__(:type, :boolean_field) ==
               :boolean

      assert Test.Relations.BasicTypes.Struct.__schema__(:type, :binary_field) == :binary
    end
  end

  describe "primary keys" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.CustomPrimaryKeySchema]
    test "handles custom primary keys" do
      defmodule Test.Relations.CustomPK do
        use Drops.Relation, repo: Drops.TestRepo, name: "custom_pk", infer: true
      end

      fields = Test.Relations.CustomPK.Struct.__schema__(:fields)

      # Should include the custom primary key and other fields
      assert :uuid in fields
      assert :name in fields

      # The default :id should still be present (Ecto's default behavior)
      assert :id in fields
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.NoPrimaryKeySchema]
    test "handles tables without primary keys" do
      defmodule Test.Relations.NoPK do
        use Drops.Relation, repo: Drops.TestRepo, name: "no_pk", infer: true
      end

      fields = Test.Relations.NoPK.Struct.__schema__(:fields)

      # Should include all fields
      # Ecto still adds this
      assert :id in fields
      assert :name in fields
      assert :value in fields
    end
  end

  describe "foreign keys and associations" do
    @tag ecto_schemas: [
           Test.Ecto.TestSchemas.AssociationsSchema,
           Test.Ecto.TestSchemas.AssociationItemSchema,
           Test.Ecto.TestSchemas.AssociationParentSchema
         ]
    test "infers foreign key fields" do
      defmodule Test.Relations.Associations do
        use Drops.Relation, repo: Drops.TestRepo, name: "associations", infer: true
      end

      fields = Test.Relations.Associations.Struct.__schema__(:fields)

      # Should include foreign key fields
      assert :id in fields
      assert :name in fields
      # belongs_to association
      assert :parent_id in fields

      # Check that foreign key has correct type
      assert Test.Relations.Associations.Struct.__schema__(:type, :parent_id) == :id
    end

    @tag ecto_schemas: [
           Test.Ecto.TestSchemas.AssociationsSchema,
           Test.Ecto.TestSchemas.AssociationItemSchema,
           Test.Ecto.TestSchemas.AssociationParentSchema
         ]
    test "infers association item foreign keys" do
      defmodule Test.Relations.AssociationItems do
        use Drops.Relation, repo: Drops.TestRepo, name: "association_items", infer: true
      end

      fields = Test.Relations.AssociationItems.Struct.__schema__(:fields)

      # Should include foreign key fields
      assert :id in fields
      assert :title in fields
      # belongs_to association
      assert :association_id in fields

      # Check that foreign key has correct type
      assert Test.Relations.AssociationItems.Struct.__schema__(:type, :association_id) ==
               :id
    end
  end

  describe "timestamp handling" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.TimestampsSchema]
    test "excludes timestamp fields from inference" do
      defmodule Test.Relations.Timestamps do
        use Drops.Relation, repo: Drops.TestRepo, name: "timestamps", infer: true
      end

      fields = Test.Relations.Timestamps.Struct.__schema__(:fields)

      # Should include regular fields but exclude timestamps
      assert :id in fields
      assert :name in fields

      # Timestamps should be excluded from inference
      refute :inserted_at in fields
      refute :updated_at in fields
    end
  end

  describe "constraints and indices" do
    @tag :skip
    test "infers fields with constraints" do
      # TODO: Create ConstraintsSchema in test_schemas.ex to enable this test
      # defmodule Test.Relations.Constraints do
      #   use Drops.Relation, repo: Drops.TestRepo, name: "constraints", infer: true
      # end

      # fields = Test.Relations.Constraints.__schema__(:fields)

      # # Should include all fields regardless of constraints
      # assert :id in fields
      # assert :email in fields
      # assert :username in fields
      # assert :age in fields

      # # Check field types
      # assert Test.Relations.Constraints.__schema__(:type, :email) == :string
      # assert Test.Relations.Constraints.__schema__(:type, :username) == :string
      # assert Test.Relations.Constraints.__schema__(:type, :age) == :integer
    end
  end

  describe "error handling" do
    test "handles non-existent table gracefully" do
      # SQLite PRAGMA table_info doesn't error for non-existent tables,
      # it just returns empty results, so the schema will have no fields
      defmodule Test.Relations.NonExistent do
        use Drops.Relation,
          repo: Drops.TestRepo,
          name: "non_existent_table",
          infer: true
      end

      fields = Test.Relations.NonExistent.Struct.__schema__(:fields)

      # Should only have the default :id field that Ecto adds
      assert fields == [:id]
    end
  end

  describe "automatic schema storage" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "automatically stores Drops.Relation.Schema" do
      defmodule Test.Relations.UsersWithSchema do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      schema = Test.Relations.UsersWithSchema.schema()

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema
      assert schema.source == "users"

      # Should have primary key information
      assert Drops.Relation.Schema.PrimaryKey.field_names(schema.primary_key) == [:id]

      # Should have field metadata
      assert length(schema.fields) > 0
      assert Enum.any?(schema.fields, &(&1.name == :name))
      assert Enum.any?(schema.fields, &(&1.name == :email))

      # Should have empty foreign keys for simple schema
      assert schema.foreign_keys == []

      # Should have empty virtual fields
      assert schema.virtual_fields == []
    end

    @tag ecto_schemas: [
           Test.Ecto.TestSchemas.AssociationsSchema,
           Test.Ecto.TestSchemas.AssociationItemSchema,
           Test.Ecto.TestSchemas.AssociationParentSchema
         ]
    test "stores schema with foreign keys and associations" do
      defmodule Test.Relations.AssociationsWithSchema do
        use Drops.Relation, repo: Drops.TestRepo, name: "associations", infer: true
      end

      schema = Test.Relations.AssociationsWithSchema.schema()

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema
      assert schema.source == "associations"

      # Should have primary key information
      assert Drops.Relation.Schema.PrimaryKey.field_names(schema.primary_key) == [:id]

      # Should have field metadata including foreign keys
      assert length(schema.fields) > 0
      parent_id_field = Enum.find(schema.fields, &(&1.name == :parent_id))
      assert parent_id_field != nil
      assert parent_id_field.type == :integer

      # Note: Database introspection cannot extract association metadata
      # since associations are defined in Ecto schema code, not in the database.
      # The generated schema from database introspection will not have associations.
      assert schema.foreign_keys == []
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.CompositePrimaryKeySchema]
    test "stores schema with composite primary key" do
      defmodule Test.Relations.CompositePKWithSchema do
        use Drops.Relation, repo: Drops.TestRepo, name: "composite_pk", infer: true
      end

      schema = Test.Relations.CompositePKWithSchema.schema()

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema
      assert schema.source == "composite_pk"

      # Note: Database introspection cannot detect composite primary keys
      # defined with @primary_key false and field-level primary_key: true
      # since SQLite PRAGMA table_info only shows one primary key column.
      # The generated schema will have the default :id primary key.
      assert Drops.Relation.Schema.PrimaryKey.field_names(schema.primary_key) == [:id]
      refute Drops.Relation.Schema.composite_primary_key?(schema)
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "schema is stored as module attribute and not computed each time" do
      defmodule Test.Relations.UsersPerformance do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Call schema() multiple times and verify it returns the same object reference
      schema1 = Test.Relations.UsersPerformance.schema()
      schema2 = Test.Relations.UsersPerformance.schema()

      # Should be the exact same object (same reference)
      assert schema1 === schema2

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema1
      assert schema1.source == "users"
    end
  end
end
